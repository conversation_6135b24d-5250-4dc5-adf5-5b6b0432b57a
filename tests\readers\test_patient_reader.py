"""
Tests for the PatientReader class (extracted from test_patient.py).
"""

from pathlib import Path
from pinnacle_io.models import Patient
from pinnacle_io.readers.patient_reader import PatientReader
from pinnacle_io.services.file_reader import FileReader


def test_read_patient_file_from_path():
    """Tests reading a valid Patient file using direct path."""
    patient = PatientReader.read_from_path(patient_path=str(Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1"))

    assert isinstance(patient, Patient)
    assert patient.patient_id == 10000
    assert patient.name == "FIRST M LAST"
    assert patient.patient_path == "Institution_1/Mount_0/Patient_1"
    assert patient.last_name == "LAST"
    assert patient.first_name == "FIRST"
    assert patient.middle_name == "M"
    assert patient.medical_record_number == "000000"
    assert patient.radiation_oncologist == "TEST, MD"
    assert patient.date_of_birth is not None
    assert patient.date_of_birth.strftime("%Y-%m-%d") == "2020-01-01"

    assert len(patient.plan_list) >= 1
    plan = patient.plan_list[0]
    assert plan.plan_id == 0
    assert plan.name == "BRAIN"
    assert plan.primary_ct_image_set_id == 0

    assert len(patient.image_set_list) >= 1
    image_set = patient.image_set_list[0]
    assert image_set.image_set_id == 0
    assert image_set.modality == "CT"
    assert image_set.image_name == "ImageSet_0"
    assert image_set.series_description == "HEAD"


def test_read_patient_file_from_path_with_service():
    """Tests reading a valid Patient file using file service."""
    test_data_dir = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1"
    file_service = FileReader(str(test_data_dir))
    patient = PatientReader.read_from_path(patient_path="", file_service=file_service)

    assert isinstance(patient, Patient)
    assert patient.patient_id == 10000
    assert patient.name == "FIRST M LAST"
    assert patient.patient_path == "Institution_1/Mount_0/Patient_1"


def test_read_patient_file_from_ids():
    """Tests reading a valid Patient file using institution and patient IDs."""
    # Use the root test data directory as the file service base
    test_data_root = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_root))
    
    patient = PatientReader.read_from_ids(
        institution_id=1,
        patient_id=1,
        file_service=file_service
    )

    assert isinstance(patient, Patient)
    assert patient.patient_id == 10000
    assert patient.name == "FIRST M LAST"
    assert patient.patient_path == "Institution_1/Mount_0/Patient_1"
    assert patient.last_name == "LAST"
    assert patient.first_name == "FIRST"
    assert patient.middle_name == "M"


def test_read_patient_file_from_ids_custom_mount():
    """Tests reading a valid Patient file using IDs with custom mount_id."""
    # Use the root test data directory as the file service base
    test_data_root = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_root))
    
    patient = PatientReader.read_from_ids(
        institution_id=1,
        patient_id=1,
        mount_id=0,  # Explicitly specify mount_id
        file_service=file_service
    )

    assert isinstance(patient, Patient)
    assert patient.patient_id == 10000
    assert patient.name == "FIRST M LAST"


def test_read_patient_file_from_ids_default_mount():
    """Tests read_from_ids with default mount_id=0."""
    # Use the root test data directory as the file service base
    test_data_root = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_root))
    
    # Test with default mount_id (should be 0)
    patient = PatientReader.read_from_ids(
        institution_id=1,
        patient_id=1,
        file_service=file_service
    )

    assert isinstance(patient, Patient)
    assert patient.patient_id == 10000
    assert patient.name == "FIRST M LAST"